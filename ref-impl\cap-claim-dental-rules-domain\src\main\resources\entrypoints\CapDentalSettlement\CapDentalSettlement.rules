Namespace CapDentalSettlement

/* on adjudicate settlement */
EntryPoint "Adjudicate Settlement validation" {
}

EntryPoint "AdjudicateSettlement" {
  "AssertClaimOverideAllowed",
  "AssertClaimOverideDenied",
  "AssertServiceOverideAllowed",
  "AssertServiceOverideDenied",
  "ClaimOverrideBasicWaitingPeriodHigherThanZero",
  "ClaimOverrideGracePeriodEqualOrHigherThanZero",
  "ClaimOverrideLateEntrantWaitingPeriodHigherThanZero",
  "ClaimOverrideMajorWaitingPeriodHigherThanZero",
  "ClaimOverrideOrthoWaitingPeriodHigherThanZero",
  "ClaimOverridePaymentInterestAmountEqualOrHigherThanZero",
  "ClaimOverridePaymentInterestDaysEqualOrHigherThanZero",
  "ClaimOverridePreventiveWaitingPeriodHigherThanZero",
  "MandatoryClaimURI",
  "ServiceOverrideCoinsurancePctEqualOrHigherThanZero",
  "ServiceOverrideConsideredAmountEqualOrHigherThanZero",
  "ServiceOverrideCopayAmountEqualOrHigherThanZero",
  "ServiceOverrideCoveredAmountEqualOrHigherThanZero",
  "ServiceOverrideDeductibleEqualOrHigherThanZero",
  "ServiceOverrideGracePeriodEqualOrHigherThanZero",
  "ServiceOverrideLateEntrantWaitingPeriodHigherThanZero",
  "ServiceOverrideMaximumAmountEqualOrHigherThanZero",
  "ServiceOverridePatientResponsibilityEqualOrHigherThanZero",
  "ServiceOverridePaymentInterestAmountEqualOrHigherThanZero",
  "ServiceOverridePaymentInterestDaysEqualOrHigherThanZero",
  "ServiceOverrideReplacementLimitHigherThanZero",
  "ServiceOverrideServiceFrequencyLimitHigherThanZero",
  "ServiceOverrideServiceWaitingPeriodHigherThanZero"
}
/* on init settlement */
EntryPoint "Init Settlement validation" {
}

EntryPoint "InitSettlement" {
  "MandatoryClaimURI"
}