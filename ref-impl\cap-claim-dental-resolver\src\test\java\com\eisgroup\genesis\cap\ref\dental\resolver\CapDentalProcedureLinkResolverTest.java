package com.eisgroup.genesis.cap.ref.dental.resolver;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.adjudication.repository.ClaimSettlementRepository;
import com.eisgroup.genesis.cap.ref.dental.link.CapDentalProcedureLinkResolver;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPatientHistoryRepository;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryEntity;
import com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalSettlementEntity;
import com.eisgroup.genesis.json.JsonEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.repository.ReadContext;
import com.eisgroup.genesis.test.utils.JsonUtils;

import com.eisgroup.genesis.test.utils.TestStreamable;
import com.google.gson.JsonObject;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class CapDentalProcedureLinkResolverTest {

    private final static String MANUAL_PROCEDURE_ID = "gentity://CapDentalPatientHistoryModelType/CapDentalPatientHistory/b7d83ccb-1678-41d2-8e2e-4aa129be2ca4/1";
    private final static String MANUAL_PROCEDURE_URI = EntityLink.generateURI("capPatient", "manualHistory", MANUAL_PROCEDURE_ID);

    private final static String HISTORY_PROCEDURE_ID = "CapDentalSettlement/ecdb89c1-bc8f-43b9-b444-34e9e076c8c5/49efe9d0-5fb5-39fb-9b44-35548bf2d20b";
    private final static String HISTORY_PROCEDURE_URI = EntityLink.generateURI("capPatient", "claimHistory", HISTORY_PROCEDURE_ID);

    // 49efe9d0-5fb5-39fb-9b44-35548bf2d20c submittedProcedure id does not exist in json mock
    private final static String HISTORY_PROCEDURE_ID_2 = "CapDentalSettlement/ecdb89c1-bc8f-43b9-b444-34e9e076c8c5/49efe9d0-5fb5-39fb-9b44-35548bf2d20c";
    private final static String HISTORY_PROCEDURE_URI_2 = EntityLink.generateURI("capPatient", "claimHistory", HISTORY_PROCEDURE_ID_2);

    @InjectMocks
    private CapDentalProcedureLinkResolver resolver;

    @Mock
    private CapDentalPatientHistoryRepository capDentalPatientHistoryRepository;

    @Mock
    private CapDentalPatientHistoryEntity capDentalPatientHistoryEntity;

    @Mock
    private ClaimSettlementRepository claimSettlementRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void shouldResolveManualEntity() {
        // given
        JsonObject patientHistoryJson = JsonUtils.load("json/patient-history.json");
        when(capDentalPatientHistoryRepository.load(any(), any())).thenReturn(Lazy.of(capDentalPatientHistoryEntity));
        when(capDentalPatientHistoryEntity.toJson()).thenReturn(patientHistoryJson);
        EntityLink<JsonEntity> jsonEntityEntityLink = new EntityLink<>(JsonEntity.class, MANUAL_PROCEDURE_URI);

        // when
        TestStreamable.create(resolver.resolve(jsonEntityEntityLink, new ReadContext.Builder().build()))
            .assertValue(entity -> entity.toJson().get("_type").getAsString().equals("CapDentalPatientHistoryEntity"));
    }

    @Test
    public void shouldResolveHistoryEntity() {
        // given
        JsonObject claimSettlementJson = JsonUtils.load("json/claim-settlement.json");
        CapDentalSettlementEntity capDentalSettlementEntity = (CapDentalSettlementEntity) ModelInstanceFactory.createInstance(claimSettlementJson);
        when(claimSettlementRepository.load(any(), any())).thenReturn(Lazy.of(capDentalSettlementEntity));
        EntityLink<JsonEntity> jsonEntityEntityLink = new EntityLink<>(JsonEntity.class, HISTORY_PROCEDURE_URI);

        // when
        TestStreamable.create(resolver.resolve(jsonEntityEntityLink, new ReadContext.Builder().build()))
            .assertValue(entity -> entity.toJson().get("_type").getAsString().equals("CapDentalProcedureEntity"));
    }

    @Test
    public void shouldReceiveException() {
        // given
        JsonObject claimSettlementJson = JsonUtils.load("json/claim-settlement.json");
        CapDentalSettlementEntity capDentalSettlementEntity = (CapDentalSettlementEntity) ModelInstanceFactory.createInstance(claimSettlementJson);
        when(claimSettlementRepository.load(any(), any())).thenReturn(Lazy.of(capDentalSettlementEntity));
        EntityLink<JsonEntity> jsonEntityEntityLink = new EntityLink<>(JsonEntity.class, HISTORY_PROCEDURE_URI_2);

        // when
        TestStreamable.create(resolver.resolve(jsonEntityEntityLink, new ReadContext.Builder().build()))
            .assertNoValues();
    }
}
