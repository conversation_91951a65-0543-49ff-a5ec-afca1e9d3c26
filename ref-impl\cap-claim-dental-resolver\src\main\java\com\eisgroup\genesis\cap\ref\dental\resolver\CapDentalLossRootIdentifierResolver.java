/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.dental.resolver;

import com.eisgroup.genesis.cap.common.resolver.CapRootIdentifierResolver;
import com.eisgroup.genesis.cap.transformation.factory.features.RootIdentifierResolver;

/**
 * {@link CapRootIdentifierResolver} implementation for Dental loss root id resolving
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalLossRootIdentifierResolver extends RootIdentifierResolver {

    private static final String DENTAL_TRANSFORMATION = "DentalLossToRootIdentifier";
    private static final String MODEL_NAME = "CapDentalLoss";

    /**
     * {@inheritDoc}
     */
//    @Override
    protected String getTransformationName() {
        return DENTAL_TRANSFORMATION;
    }

    /**
     * {@inheritDoc}
     */
//    @Override
    protected String getModelName() {
        return MODEL_NAME;
    }
}
