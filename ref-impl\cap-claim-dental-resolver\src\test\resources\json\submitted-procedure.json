{"dentist": {"dentistID": "95197", "providerTIN": "10-1000011", "_type": "CapDentalDentistEntity", "_key": {"id": "1dc9104c-3893-3fe1-b6a5-c2b00ef0ea0f", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b"}}, "certPolicyInfo": {"riskStateCd": "FL", "_modelName": "CapDentalSettlement", "_type": "CapDentalPolicyInfoEntity", "capPolicyId": "capPolicy://CapUP/geroot://UnverifiedPolicy/CapDentalUnverifiedPolicy//a8a4962b-5ebe-41e6-9e51-2515af90ec4b", "capPolicyVersionId": "capPolicy://CapUP/gentity://UnverifiedPolicy/CapDentalUnverifiedPolicy//a8a4962b-5ebe-41e6-9e51-2515af90ec4b/1", "coinsurances": [{"coinsuranceServiceType": "Preventive", "coinsuranceINPct": 50, "coinsuranceOONPct": 50, "_type": "CapDentalPolicyInfoCoinsuranceEntity", "_key": {"id": "afcf8562-4730-3a74-b46d-b1907d333d76", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "f19b0267-016d-34f3-a238-9e0e59e695b3"}}, {"coinsuranceServiceType": "Major", "coinsuranceINPct": 93, "coinsuranceOONPct": 77, "_type": "CapDentalPolicyInfoCoinsuranceEntity", "_key": {"id": "1dfcd145-7f99-3c73-8313-5a5dd65bcb60", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "f19b0267-016d-34f3-a238-9e0e59e695b3"}}], "insureds": [{"isFullTimeStudent": false, "relationshipToPrimaryInsuredCd": "Dependent<PERSON><PERSON><PERSON>", "registryTypeId": "geroot://Customer/INDIVIDUALCUSTOMER//{{rootId_Individual}}", "_type": "CapDentalPolicyInfoInsuredDetailsEntity", "_key": {"id": "4ee624a6-2dad-3c79-a6a9-9f2d6eb93ca0", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "f19b0267-016d-34f3-a238-9e0e59e695b3"}}], "isVerified": false, "plan": "High", "planCategory": "PPO", "policyPaidToDate": "2022-10-27", "policyPaidToDateWithGracePeriod": "2022-10-27", "policyStatus": "Active", "productCd": "DNIndividual", "term": {"effectiveDate": "2020-10-27T13:44:24.154Z", "expirationDate": "2023-10-27T13:44:24.154Z", "_type": "Term", "_key": {"id": "bf7ebba1-8394-3a45-ad5f-ca2e9f2f2975", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "f19b0267-016d-34f3-a238-9e0e59e695b3"}}, "_key": {"id": "f19b0267-016d-34f3-a238-9e0e59e695b3", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b"}}, "procedureCode": "D0330", "submittedFee": {"amount": 114.17, "currency": "USD"}, "dateOfService": "2022-03-30", "toothCodes": ["6"], "predetInd": false, "ortho": {"downPayment": {"amount": 100, "currency": "USD"}, "appliancePlacedDate": "2022-02-18", "orthoFrequencyCd": "OneTime", "orthoMonthQuantity": 2, "_type": "CapDentalOrthodonticEntity", "_key": {"id": "a209ab1d-6b43-3d2a-b08c-b978361b413a", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b"}}, "diagnosisCodes": [{"code": "string1", "qualifier": "AB", "_type": "CapDentalDiagnosisCodeEntity", "_key": {"id": "bee272a8-2313-3c9f-b4dc-a7d85034d81e", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b"}}], "_type": "CapDentalProcedureEntity", "_key": {"id": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "360ac470-c2e4-3288-8549-176e74e702e0"}}