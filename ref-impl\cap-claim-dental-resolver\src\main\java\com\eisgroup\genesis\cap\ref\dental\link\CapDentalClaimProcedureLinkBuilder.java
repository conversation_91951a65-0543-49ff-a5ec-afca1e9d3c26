/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.dental.link;

import java.util.Optional;

import com.eisgroup.genesis.factory.json.ModelFactory;
import com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalProcedureEntity;
import com.eisgroup.genesis.json.key.EntityKey;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkBuilder;
import com.eisgroup.genesis.json.link.LinkingParams;

/**
 * Link builder for manual procedure entity. Follows pattern <code>capPatient://claimHistory/<settlementType>/<settlementRootId>/<submittedProcedureId>'</code>.
 * Example:
 * <p>
 *    capPatient://claimHistory/CapDentalSettlement/ecdb89c1-bc8f-43b9-b444-34e9e076c8c5/49efe9d0-5fb5-39fb-9b44-35548bf2d20b
 * </p>
 *
 * <AUTHOR>
 * @since 22.8
 */
public class CapDentalClaimProcedureLinkBuilder implements EntityLinkBuilder<CapDentalProcedureEntity> {

    @Override
    public boolean supports(Class<?> aClass) {
        return CapDentalProcedureEntity.class.isAssignableFrom(aClass);
    }

    @Override
    public EntityLink<CapDentalProcedureEntity> createLink(CapDentalProcedureEntity entity) {
        return createLink(entity, LinkingParams.empty());
    }

    @Override
    public EntityLink<CapDentalProcedureEntity> createLink(CapDentalProcedureEntity entity, LinkingParams linkingParams) {
        return Optional.ofNullable(entity.getModelFactory())
                .map(ModelFactory::getModelName)
                .flatMap(modelName -> Optional.ofNullable(entity.getKey())
                        .map(EntityKey::getRootId)
                        .flatMap(settlementId -> Optional.ofNullable(entity.getKey())
                                .map(EntityKey::getId)
                                .flatMap(procedureId -> Optional.of(new ClaimProcedureLink(modelName, settlementId, procedureId)))))
                .map(link -> new EntityLink<>(CapDentalProcedureEntity.class, link.toURI()))
                .orElseThrow(() -> new IllegalArgumentException(String.format("Unable to create link with nulls. Key:%s, modelFactory %s",
                        entity.getKey(), entity.getModelFactory())));
    }
}
