Namespace CapDentalLoss

Rule "DownPaymentCannotBeNegative" On CapDentalOrthodonticEntity.downPayment {
  Description "EISDEVTS-39873"
  Assert CapDentalOrthodonticEntity.downPayment >= 0
  Error "DownPaymentCannotBeNegative": "Down Payment cannot be a negative value."
}

Rule "MandatoryOrthodonticFrequency" On CapDentalOrthodonticEntity.orthoFrequencyCd {
  Description "Field is mandatory when Claim Type is Orthodontic Services"
  When CapDentalClaimDataEntity.transactionType == "OrthodonticServices"
  Set Mandatory
  Error "MandatoryOrthodonticFrequency": "Payment Frequency is required."
}

Rule "HideDateApplicancePlaced" On CapDentalOrthodonticEntity.appliancePlacedDate {
  Description "Display only when Claim Type is Orthodontic Services"
  When CapDentalClaimDataEntity.transactionType != "OrthodonticServices"
  Set Hidden
}

Rule "HideDownPaymentAmount" On CapDentalOrthodonticEntity.downPayment {
  Description "Display only when Claim Type is Orthodontic Services"
  When CapDentalClaimDataEntity.transactionType != "OrthodonticServices"
  Set Hidden
}
