Namespace CapDentalLoss

Rule "MandatoryAlternatePayee" On CapDentalClaimDataEntity.alternatePayeeRole {
  Description "EISDEVTS-40549"
  When CapDentalClaimDataEntity.payeeType = "AlternatePayee"
  Set Mandatory
  Error "MandatoryAlternatePayee": "alternatePayee is mandatory."
}

Rule "CleanClaimDateCannotBeBeforeFutureDate" On CapDentalClaimDataEntity.cleanClaimDate {
  Description "EISDEVTS-40089"
  When CapDentalClaimDataEntity.cleanClaimDate != NULL
  Assert Today() >= CapDentalClaimDataEntity.cleanClaimDate
  Error "CleanClaimDateCannotBeBeforeFutureDate": "cleanClaimDate cannot be in the future."
}

Rule "CleanClaimDateCannotBeBeforeReceivedDate" On CapDentalClaimDataEntity.cleanClaimDate {
  Description "EISDEVTS-40089"
  When CapDentalClaimDataEntity.cleanClaimDate != NULL
  Assert CapDentalClaimDataEntity.cleanClaimDate >= CapDentalClaimDataEntity.receivedDate
  Error "CleanClaimDateCannotBeBeforeReceivedDate": "cleanClaimDate cannot be before the receivedDate."
}

Rule "ResetCleanClaimDate" On CapDentalClaimDataEntity.cleanClaimDate {
  Description "EISDEVTS-40089"
  When CapDentalClaimDataEntity.transactionType = "ActualServices" AND CapDentalClaimDataEntity.source = "EDI"
  Reset To Today()
}

Rule "MandatoryClaimPatient" On CapDentalClaimDataEntity.patientRole {
  Description "EISDEVTS-39836"
  Set Mandatory
  Error "MandatoryClaimPatient": "Patient is mandatory."
}

Rule "DefaultClaimPayeeType" On CapDentalClaimDataEntity.payeeType {
  Description "EISDEVTS-77162"
  Default To "Provider"
}

Rule "MandatoryClaimPayeeType" On CapDentalClaimDataEntity.payeeType {
  Description "EISDEVTS-39836"
  Set Mandatory
  Error "MandatoryClaimPayeeType": "Payee Type is mandatory."
}

Rule "MandatoryClaimPolicyHolder" On CapDentalClaimDataEntity.policyholderRole {
  Description "EISDEVTS-39836"
  Set Mandatory
  Error "MandatoryClaimPolicyHolder": "Policy Holder is mandatory."
}

Rule "MandatoryClaimProvider" On CapDentalClaimDataEntity.providerRole {
  Description "EISDEVTS-39836, EISDEVTS-37177"
  When CapDentalClaimDataEntity.isUnknownOrIntProvider != true
  Set Mandatory
  Error "MandatoryClaimProvider": "Provider is mandatory when the isUnknownOrIntProvider flag is not true."
}

Rule "DefaultReceivedDate" On CapDentalClaimDataEntity.receivedDate {
  Description "EISDEVTS-77162"
  Default To Today()
}

Rule "MandatoryClaimReceivedDate" On CapDentalClaimDataEntity.receivedDate {
  Description "EISDEVTS-39836"
  Set Mandatory
  Error "MandatoryClaimReceivedDate": "Received Date is mandatory."
}

Rule "ReceivedDateCannotBeInFuture" On CapDentalClaimDataEntity.receivedDate {
  Description "EISDEVTS-40089"
  Assert Today() >= CapDentalClaimDataEntity.receivedDate
  Error "ReceivedDateCannotBeInFuture": "Received Date cannot be in the future."
}

Rule "MandatoryClaimSource" On CapDentalClaimDataEntity.source {
  Description "EISDEVTS-39836"
  Set Mandatory
  Error "MandatoryClaimSource": "Source for the claim is mandatory."
}

Rule "DefaultSource" On CapDentalClaimDataEntity.source {
  Description "Set default value"
  Default To "NONEDI"
}

Rule "MandatoryClaimTransactionType" On CapDentalClaimDataEntity.transactionType {
  Description "EISDEVTS-39836"
  Set Mandatory
  Error "MandatoryClaimTransactionType": "Claim Type is mandatory."
}

Rule "SuspendLossOnlyAvailableForOrtho" On CapDentalClaimDataEntity.transactionType {
  Description "EISDEVTS-39345"
  Assert CapDentalClaimDataEntity.transactionType = "OrthodonticServices"
  Error "SuspendLossOnlyAvailableForOrtho": "suspendLoss command cannot be performed if transactionType is not OrthodonticServices."
}

Rule "DefaultTransactionType" On CapDentalClaimDataEntity.transactionType {
  Description "GENESIS-353442"
  Default To "ActualServices"
}


Rule "MandatoryClaimPolicyId" On CapDentalLossEntity.policyId {
  Description "EISDEVTS-49934"
  Set Mandatory
  Error "MandatoryClaimPolicyId": "Policy is mandatory."
}

Rule "ResetClaimReopenReason" On CapDentalLossEntity.reasonCd {
  Description "EISDEVTS-46839"
  Reset To null
}



