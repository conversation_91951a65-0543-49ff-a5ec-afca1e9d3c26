package com.eisgroup.genesis.cap.ref.dental.resolver;

import com.eisgroup.genesis.cap.ref.dental.link.CapDentalManualProcedureLinkBuilder;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.google.gson.JsonObject;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;

public class CapDentalManualProcedureLinkBuilderTest {

    private static final String PATIENT_HISTORY_LINK_URI = "capPatient://manualHistory/geroot://CapDentalPatientHistoryModelType/CapDentalPatientHistory/1ae757f3-8454-4e8a-a245-9b4c195bda91/1";

    @InjectMocks
    private CapDentalManualProcedureLinkBuilder capDentalManualProcedureLinkBuilder;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void shouldCreateManualProcedureLink() {
        // given
        JsonObject patientHistoryJson = JsonUtils.load("json/patient-history.json");
        CapDentalPatientHistoryEntity patientHistoryEntity = (CapDentalPatientHistoryEntity) ModelInstanceFactory.createInstance(patientHistoryJson);

        // when
        EntityLink<CapDentalPatientHistoryEntity> result = capDentalManualProcedureLinkBuilder.createLink(patientHistoryEntity);

        // then
        assertEquals(result.getURIString(), PATIENT_HISTORY_LINK_URI);
    }
}
