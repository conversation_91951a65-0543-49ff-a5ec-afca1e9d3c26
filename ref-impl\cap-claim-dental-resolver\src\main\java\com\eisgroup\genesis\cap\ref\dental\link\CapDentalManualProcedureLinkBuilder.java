/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.dental.link;

import java.util.Optional;

import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryEntity;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkBuilder;
import com.eisgroup.genesis.json.link.LinkingParams;

/**
 * Link builder for manual procedure entity. Follows pattern <code>capPatient://manualHistory/geroot://<Manual Patient history>'</code>.
 * Example:
 * <p>
 *    capPatient://manualHistory/geroot://CapDentalPatientHistoryModelType/CapDentalPatientHistory/b7d83ccb-1678-41d2-8e2e-4aa129be2ca4/1
 * </p>
 *
 * <AUTHOR>
 * @since 22.8
 */
public class CapDentalManualProcedureLinkBuilder implements EntityLinkBuilder<CapDentalPatientHistoryEntity> {

    @Override
    public boolean supports(Class<?> aClass) {
        return CapDentalPatientHistoryEntity.class.isAssignableFrom(aClass);
    }

    @Override
    public EntityLink<CapDentalPatientHistoryEntity> createLink(CapDentalPatientHistoryEntity entity) {
        return createLink(entity, LinkingParams.empty());
    }

    @Override
    public EntityLink<CapDentalPatientHistoryEntity> createLink(CapDentalPatientHistoryEntity entity, LinkingParams linkingParams) {
        return Optional.ofNullable(entity.getKey())
                .map(RootEntityKey::getId)
                .map(patientHistoryId -> new ManualProcedureLink(entity.getModelType(), entity.getModelName(), patientHistoryId))
                .map(link -> new EntityLink<>(CapDentalPatientHistoryEntity.class, link.toURI()))
                .orElseThrow(() -> new IllegalArgumentException(String.format("Unable to create link with nulls. Key:%s",
                        entity.getKey())));
    }
}
