/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.dental.resolver;

import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import org.junit.Before;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class CapDentalSettlementRootIdentifierResolverTest {

    private static final String DENTAL_URI = "gentity://CapLoss/CapDentalLoss//a240b35b-bf53-4f23-aac2-3f7fb673bc57/1";
    private static final String MODEL_NAME = "CapDentalSettlement";
    private static final String MODEL_VERSION = "1";
    private static final String TRANSFORMATION_MODEL = "DentalSettlementRootIdentifierTransformationModel";

    @InjectMocks
    private CapDentalSettlementRootIdentifierResolver resolver;

    @Mock
    private ModeledTransformationService modeledTransformer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }
    //TODO-For:GENESIS-358942
//    @Test
//    public void shouldResolverDentalLossURI() {
//        // given
//        CapDentalSettlementEntity root = (CapDentalSettlementEntity) ModelInstanceFactory.createRootInstance(MODEL_NAME, MODEL_VERSION);
//        when(modeledTransformer.transform(any(TransformationModel.class), eq(root)))
//                .thenReturn(Single.just(createRootIdentifier()));
//
//        // when
//        Streamable<String> result = resolver.resolve(root);
//
//        // then
//        result.test().assertValueCount(1).assertValue(DENTAL_URI);
//    }
//
//    @Test
//    public void shouldCheckThatResolverSupportsModel() {
//        // when
//        boolean result = resolver.supports(MODEL_NAME, null);
//
//        // then
//        assertThat(result, equalTo(true));
//    }
//
//    private CapRootIdentifier createRootIdentifier() {
//        return CapRootIdentifierBuilder.createRoot(TRANSFORMATION_MODEL, MODEL_VERSION)
//                .addLinks(new EntityLink<>(RootEntity.class, DENTAL_URI)).build();
//    }

}