/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.dental.link;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.adjudication.repository.ClaimSettlementRepository;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPatientHistoryRepository;
import com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalSettlementEntity;
import com.eisgroup.genesis.json.JsonEntity;
import com.eisgroup.genesis.json.key.EntityKey;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolver;
import com.eisgroup.genesis.json.wrapper.DefaultJsonWrapperFactory;
import com.eisgroup.genesis.json.wrapper.JsonWrapperFactory;
import com.eisgroup.genesis.repository.ReadContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

/**
 * Resolves dental procedure entities. Link format should match one of these patterns:
 * <ul>
 *     <li>capPatient://manualHistory/geroot://<Manual Patient history></li>
 *     <li>capPatient://claimHistory/<settlementType>/<settlementRootId>/<submittedProcedureId></li>
 * </ul>
 * Examples:
 * <ul>
 *     <li>capPatient://manualHistory/geroot://CapDentalPatientHistoryModelType/CapDentalPatientHistory/b7d83ccb-1678-41d2-8e2e-4aa129be2ca4/1</li>
 *     <li>capPatient://claimHistory/CapDentalSettlement/ecdb89c1-bc8f-43b9-b444-34e9e076c8c5/49efe9d0-5fb5-39fb-9b44-35548bf2d20b</li>
 * </ul>
 *
 *
 * Resolvers will try to retrieve entities from {@link CapDentalPatientHistoryRepository}, {@link ClaimSettlementRepository}
 * repositories respectively.
 * Supported procedures types are manualHistory, claimHistory.
 * Possible resolved entity types are CapDentalProcedureEntity, CapDentalPatientHistoryEntity.
 *
 * <AUTHOR>
 * @since 22.8
 */
public class CapDentalProcedureLinkResolver implements EntityLinkResolver<JsonEntity> {

    private static final String MANUAL_HISTORY_TYPE = "manualHistory";

    private static final String CLAIM_HISTORY_TYPE = "claimHistory";

    private static final List<String> supportedTypes = List.of(MANUAL_HISTORY_TYPE, CLAIM_HISTORY_TYPE);

    private final CapDentalPatientHistoryRepository capDentalPatientHistoryRepository;

    private final ClaimSettlementRepository<CapDentalSettlementEntity> claimSettlementRepository;

    private final JsonWrapperFactory wrapperFactory = new DefaultJsonWrapperFactory();

    public CapDentalProcedureLinkResolver(CapDentalPatientHistoryRepository capDentalPatientHistoryRepository,
                                          ClaimSettlementRepository claimSettlementRepository) {
        this.capDentalPatientHistoryRepository = capDentalPatientHistoryRepository;
        this.claimSettlementRepository = claimSettlementRepository;
    }

    @Override
    public String getLinkScheme() {
        return "capPatient";
    }

    @Override
    public Lazy<JsonEntity> resolve(EntityLink<JsonEntity> entityLink, ReadContext readContext) {
        return Lazy.defer(() -> {
            String entityType = entityLink.getURI().getHost();

            if (StringUtils.equals(MANUAL_HISTORY_TYPE, entityType)) {
                ManualProcedureLink manualProcedureLink = new ManualProcedureLink(entityLink);
                return resolveManual(
                        new RootEntityKey(manualProcedureLink.getRootId(), manualProcedureLink.getVersion()),
                        manualProcedureLink.getModelName()
                );
            }

            if (StringUtils.equals(CLAIM_HISTORY_TYPE, entityType)) {
                ClaimProcedureLink claimProcedureLink = new ClaimProcedureLink(entityLink);
                return resolveHistory(
                        new RootEntityKey(claimProcedureLink.getSettlementId(), 1),
                        claimProcedureLink.getModelName(),
                        claimProcedureLink.getProcedureId()
                );
            }

            // not supported
            return Lazy.error(() -> new IllegalArgumentException("Unsupported procedure type, expected types: " + supportedTypes.toString()));
        });
    }

    @Override
    public Streamable<JsonEntity> resolve(Collection<EntityLink<JsonEntity>> collection, ReadContext readContext) {
        return Streamable.from(collection).flatMap(link -> resolve(link, readContext));
    }

    protected Lazy<JsonEntity> resolveManual(RootEntityKey key, String modelName) {
        return capDentalPatientHistoryRepository.load(key, modelName)
                .map(entity -> wrapperFactory.wrap(entity.toJson(), JsonEntity.class));
    }

    protected Lazy<JsonEntity> resolveHistory(RootEntityKey key, String modelName, UUID procedureId) {
        return claimSettlementRepository.load(key, modelName)
                .map(CapDentalSettlementEntity::getSettlementLossInfo)
            .flatMap(settlementLossInfo -> {
                    if (CollectionUtils.isEmpty(settlementLossInfo.getSubmittedProcedures())) {
                        return Lazy.empty();
                    }
                    return Lazy.from(settlementLossInfo.getSubmittedProcedures());
                })
                .filter(submittedProcedure -> procedureId.equals(Optional.ofNullable(submittedProcedure.getKey()).map(EntityKey::getId).orElse(null)))
                .map(procedure -> wrapperFactory.wrap(procedure.toJson(), JsonEntity.class));
    }
}
