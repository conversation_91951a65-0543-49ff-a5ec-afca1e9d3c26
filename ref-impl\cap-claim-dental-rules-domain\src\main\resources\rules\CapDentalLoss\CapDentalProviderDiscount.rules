Namespace CapDentalLoss

Rule "DiscountAmountCannotBeNegative" On CapDentalProviderDiscountEntity.discountAmount {
  Description "EISDEVTS-39873"
  Assert CapDentalProviderDiscountEntity.discountAmount >= 0
  Error "DiscountAmountCannotBeNegative": "discountAmount cannot be negative."
}

Rule "MandatoryClaimDiscountAmount" On CapDentalProviderDiscountEntity.discountAmount {
  Description "EISDEVTS-39836"
  When CapDentalProviderDiscountEntity.discountType = "Amount"
  Set Mandatory
  Error "MandatoryClaimDiscountAmount": "discountAmount is mandatory."
}

Rule "MandatoryClaimDiscountName" On CapDentalProviderDiscountEntity.discountName {
  Description "EISDEVTS-39836"
  When CapDentalClaimDataEntity.providerDiscount != NULL
  Set Mandatory
  Error "MandatoryClaimDiscountName": "discountName is mandatory."
}

Rule "MandatoryClaimDiscountPercentage" On CapDentalProviderDiscountEntity.discountPercentage {
  Description "EISDEVTS-39836"
  When CapDentalProviderDiscountEntity.discountType = "Percentage"
  Set Mandatory
  Error "MandatoryClaimDiscountPercentage": "discountPercentage is mandatory."
}

Rule "MandatoryClaimDiscountType" On CapDentalProviderDiscountEntity.discountType {
  Description "EISDEVTS-39836"
  When CapDentalClaimDataEntity.providerDiscount != NULL
  Set Mandatory
  Error "MandatoryClaimDiscountType": "discountType is mandatory."
}
