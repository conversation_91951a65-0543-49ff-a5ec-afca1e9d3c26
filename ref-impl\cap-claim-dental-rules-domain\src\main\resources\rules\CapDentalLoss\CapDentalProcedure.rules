Namespace CapDentalLoss

Rule "DateOfServiceCannotBeAfterCleanClaimDateWhenCleanClaimDateIsSet" On CapDentalProcedureEntity.dateOfService {
  Description "EISDEVTS-40089"
  When CapDentalClaimDataEntity.cleanClaimDate != NULL AND CapDentalProcedureEntity.dateOfService != NULL
  Assert CapDentalProcedureEntity.dateOfService <= CapDentalClaimDataEntity.cleanClaimDate
  Error "DateOfServiceCannotBeAfterCleanClaimDateWhenCleanClaimDateIsSet": "dateOfService cannot be after the cleanClaimDate."
}

Rule "DateOfServiceCannotBeAfterReceivedDateWhenCleanClaimDateIsBlank" On CapDentalProcedureEntity.dateOfService {
  Description "Date of Service cannot be after the Received Date."
  When CapDentalClaimDataEntity.cleanClaimDate = NULL AND CapDentalProcedureEntity.dateOfService != NULL AND CapDentalClaimDataEntity.receivedDate!= NULL
  Assert CapDentalProcedureEntity.dateOfService <= CapDentalClaimDataEntity.receivedDate
  Error "DateOfServiceCannotBeAfterReceivedDateWhenCleanClaimDateIsBlank": "Date of Service cannot be after the Received Date."
}

Rule "DateOfServiceCannotBeInFuture" On CapDentalProcedureEntity.dateOfService {
  Description "Date of Service cannot be future date."
  Assert Today() >= CapDentalProcedureEntity.dateOfService
  Error "DateOfServiceCannotBeInFuture": "Date of Service cannot be future date."
}

Rule "MandatoryProcedureDateOfService" On CapDentalProcedureEntity.dateOfService {
  Description "Field is mandatory"
  Set Mandatory
  Error "MandatoryProcedureDateOfService": "Date of Service is required."
}

Rule "DefaultProcedureActual" On CapDentalProcedureEntity.predetInd {
  Description "EISDEVTS-39873"
  When CapDentalClaimDataEntity.transactionType = "ActualServices" || CapDentalClaimDataEntity.transactionType = "OrthodonticServices"
  Default To false
}

Rule "DefaultProcedurePredetInd" On CapDentalProcedureEntity.predetInd {
  Description "EISDEVTS-39836"
  When CapDentalClaimDataEntity.transactionType = "PredeterminationActualServices" || CapDentalClaimDataEntity.transactionType = "PredeterminationOrthodontics"
  Default To true
}

Rule "MandatoryProcedureCode" On CapDentalProcedureEntity.procedureCode {
  Description "Field is mandatory"
  Set Mandatory
  Error "MandatoryProcedureCode": "Procedure Code is required."
}

Rule "ProcedureQuantityMax" On CapDentalProcedureEntity.quantity {
  Description "Quantity <=99"
  Assert CapDentalProcedureEntity.quantity <= 99
  Error "ProcedureQuantityMax": "Quantity must not be greater than 99."
}

Rule "ProcedureQuantityMin" On CapDentalProcedureEntity.quantity {
  Description "Quantity >=1"
  Assert CapDentalProcedureEntity.quantity >= 1
  Error "ProcedureQuantityMin": "Quantity has to be equal or more than 1."
}

Rule "MandatorySubmittedFee" On CapDentalProcedureEntity.submittedFee {
  Description "Field is mandatory"
  Set Mandatory
  Error "MandatorySubmittedFee": "Charges is required."
}

Rule "SubmittedFeeCannotBeNegative" On CapDentalProcedureEntity.submittedFee {
  Description "Charges cannot be negative"
  Assert CapDentalProcedureEntity.submittedFee >= 0
  Error "SubmittedFeeCannotBeNegative": "Charges cannot be a negative value."
}

Rule "MandatoryProcedureQuantity" On CapDentalProcedureEntity.quantity {
  Description "Field is mandatory"
  Set Mandatory
  Error "MandatoryProcedureQuantity": "Quantity is required."
}


Rule "HidePriorProsthesisPlacement" On CapDentalProcedureEntity.priorProsthesisPlacementDate {
  Description "Display only when Claim Type is Orthodontic Services"
  When CapDentalClaimDataEntity.transactionType != "OrthodonticServices"
  Set Hidden
}