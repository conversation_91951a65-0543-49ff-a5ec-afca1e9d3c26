<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
        <artifactId>ms-claim-dental-ref-pom</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-resolver</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-resolver</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-repository</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.adjudication</groupId>
            <artifactId>cap-adjudication-repository</artifactId>
        </dependency>

        <!-- testing -->
        <dependency>
            <groupId>com.eisgroup.genesis.utils</groupId>
            <artifactId>testing-utils</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>