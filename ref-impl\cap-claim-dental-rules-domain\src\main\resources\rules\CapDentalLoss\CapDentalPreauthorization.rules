Namespace CapDentalLoss

Rule "AuthorizationPeriodValidation" On CapDentalPreauthorizationEntity.authorizationPeriod {
  Description "EISDEVTS-39873"
  Assert CapDentalPreauthorizationEntity.authorizationPeriod.endDate >= CapDentalPreauthorizationEntity.authorizationPeriod.startDate
  Error "AuthorizationPeriodValidation": "authorizationPeriod.endDate must be later than authorizationPeriod.startDate."
}

Rule "MandatoryAuthorizationPeriod" On CapDentalPreauthorizationEntity.authorizationPeriod {
  Description "EISDEVTS-39836"
  When isProcedureAuthorized = true
  Set Mandatory
  Error "MandatoryAuthorizationPeriod": "authorizationPeriod is mandatory."
}

Rule "MandatoryAuthorizationBy" On CapDentalPreauthorizationEntity.authorizedBy {
  Description "EISDEVTS-39836"
  When isProcedureAuthorized = true
  Set Mandatory
  Error "MandatoryAuthorizationBy": "authorizationBy is mandatory."
}