Namespace CapDentalLoss


Rule "MandatoryActualOrPredeterminationActualServicesForClaim" On CapDentalDetailEntity.submittedProcedures {
  Description "EISDEVTS-39873"
  When CapDentalClaimDataEntity.transactionType = "ActualServices" || CapDentalClaimDataEntity.transactionType = "PredeterminationActualServices"
  Assert CapDentalDetailEntity.submittedProcedures != NULL && Count(CapDentalDetailEntity.submittedProcedures) > 0
  Error "MandatoryActualOrPredeterminationActualServicesForClaim": "Submitted Procedures Details are required."
}

Rule "MandatoryOneOrthoServiceForClaim" On CapDentalDetailEntity.submittedProcedures {
  Description "Ertho details are required when transactionType is OrthodonticServices or PredeterminationOrthodontics and ortho is not null"
  When CapDentalClaimDataEntity.transactionType = "OrthodonticServices" || CapDentalClaimDataEntity.transactionType = "PredeterminationOrthodontics"
  Assert Count(submittedProcedures) = 1 && submittedProcedures[0].ortho != null
  Error "MandatoryOneOrthoServiceForClaim": "ortho details are required and only one Ortho service can be provided."
}
