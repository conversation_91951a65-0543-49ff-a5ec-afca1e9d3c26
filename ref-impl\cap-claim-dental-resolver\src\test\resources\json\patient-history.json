{"_key": {"rootId": "1ae757f3-8454-4e8a-a245-9b4c195bda91", "revisionNo": 1}, "_type": "CapDentalPatientHistoryEntity", "_modelName": "CapDentalPatientHistory", "_modelVersion": "1", "_modelType": "CapDentalPatientHistoryModelType", "patientHistoryData": {"accessTrackInfo": {"_key": {"id": "9e7602ec-b6f5-3d53-bf82-b4886d27bf63", "rootId": "1ae757f3-8454-4e8a-a245-9b4c195bda91", "revisionNo": 1, "parentId": "98dd0192-5a3d-3f82-8388-0acbf8c612fa"}, "_type": "CapDentalAccessTrackInfoEntity", "createdBy": "qa", "createdOn": "2022-05-31T07:44:22.269Z", "updatedBy": "qa", "updatedOn": "2022-05-31T07:44:22.269Z"}, "providerData": {"providerBusinessName": "string", "inOutNetwork": "string", "providerTIN": "string", "_type": "CapDentalProviderDataEntity", "_key": {"id": "d4f2ce69-a911-3142-99ea-2b279ebf711a", "rootId": "1ae757f3-8454-4e8a-a245-9b4c195bda91", "revisionNo": 1, "parentId": "98dd0192-5a3d-3f82-8388-0acbf8c612fa"}}, "serviceData": {"procedureType": "string", "benefitAmount": {"amount": 0, "currency": "string"}, "remarkCodes": ["string"], "quantity": 0, "deductibleAmount": {"amount": 0, "currency": "string"}, "decision": "string", "coinsuranceAmount": {"amount": 0, "currency": "string"}, "submittedAmount": {"amount": 0, "currency": "string"}, "authorizationPeriod": {"endDate": "2022-04-13T11:11:33.275Z", "startDate": "2022-04-13T11:11:33.275Z", "_type": "Period", "_key": {"id": "1650fefe-305f-36b4-9cce-0bb6fd291533", "rootId": "1ae757f3-8454-4e8a-a245-9b4c195bda91", "revisionNo": 1, "parentId": "11a07867-8a22-3f08-b1a0-f7637fa352ba"}}, "cdtSubmittedCd": "string", "copayAmount": {"amount": 0, "currency": "string"}, "cdtCoveredCd": "string", "consideredAmount": {"amount": 0, "currency": "string"}, "toothArea": "string", "toothCodes": ["toothcode1"], "coveredAmount": {"amount": 0, "currency": "string"}, "paymentDate": "2022-04-13T11:11:33.275Z", "DOSDate": "2022-04-13T11:11:33.275Z", "_type": "CapDentalServiceDataEntity", "_key": {"id": "11a07867-8a22-3f08-b1a0-f7637fa352ba", "rootId": "1ae757f3-8454-4e8a-a245-9b4c195bda91", "revisionNo": 1, "parentId": "98dd0192-5a3d-3f82-8388-0acbf8c612fa"}}, "claimData": {"policyNumber": "string", "patientNumber": "string", "planCategory": "string", "claimNumber": "string", "_type": "CapDentalClaimDataEntity", "_key": {"id": "83380fbe-efc1-3129-88b8-6be129c4ee1a", "rootId": "1ae757f3-8454-4e8a-a245-9b4c195bda91", "revisionNo": 1, "parentId": "98dd0192-5a3d-3f82-8388-0acbf8c612fa"}}, "_type": "CapDentalPatientHistoryDataEntity", "_key": {"id": "98dd0192-5a3d-3f82-8388-0acbf8c612fa", "rootId": "1ae757f3-8454-4e8a-a245-9b4c195bda91", "revisionNo": 1, "parentId": "1ae757f3-8454-4e8a-a245-9b4c195bda91"}}, "_timestamp": "2022-05-31T07:44:22.499Z"}