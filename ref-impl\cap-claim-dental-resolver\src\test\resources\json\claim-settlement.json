{"_key": {"rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1}, "_modelName": "CapDentalSettlement", "_modelType": "CapSettlement", "_modelVersion": "1", "_timestamp": "2022-05-25T10:25:28.044Z", "_type": "CapDentalSettlementEntity", "accessTrackInfo": {"_key": {"id": "065935a4-ba19-3944-b79e-ea72b4626990", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5"}, "_type": "AccessTrackInfo", "createdBy": "workflow-service", "createdOn": "2022-05-25T10:25:27.156Z", "updatedBy": "workflow-service", "updatedOn": "2022-05-25T10:25:27.156Z"}, "claimLossIdentification": {"_uri": "gentity://CapLoss/CapDentalLoss//8c6248a9-dee1-45b4-821c-9c06abb7dce4/1"}, "settlementLossInfo": {"submittedProcedures": [{"dentist": {"dentistID": "95197", "providerTIN": "10-1000011", "_type": "CapDentalDentistEntity", "_key": {"id": "1dc9104c-3893-3fe1-b6a5-c2b00ef0ea0f", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b"}}, "certPolicyInfo": {"riskStateCd": "FL", "_modelName": "CapDentalSettlement", "_type": "CapDentalPolicyInfoEntity", "capPolicyId": "capPolicy://CapUP/geroot://UnverifiedPolicy/CapDentalUnverifiedPolicy//a8a4962b-5ebe-41e6-9e51-2515af90ec4b", "capPolicyVersionId": "capPolicy://CapUP/gentity://UnverifiedPolicy/CapDentalUnverifiedPolicy//a8a4962b-5ebe-41e6-9e51-2515af90ec4b/1", "coinsurances": [{"coinsuranceServiceType": "Preventive", "coinsuranceINPct": 50, "coinsuranceOONPct": 50, "_type": "CapDentalPolicyInfoCoinsuranceEntity", "_key": {"id": "afcf8562-4730-3a74-b46d-b1907d333d76", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "f19b0267-016d-34f3-a238-9e0e59e695b3"}}, {"coinsuranceServiceType": "Major", "coinsuranceINPct": 93, "coinsuranceOONPct": 77, "_type": "CapDentalPolicyInfoCoinsuranceEntity", "_key": {"id": "1dfcd145-7f99-3c73-8313-5a5dd65bcb60", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "f19b0267-016d-34f3-a238-9e0e59e695b3"}}], "insureds": [{"isFullTimeStudent": false, "relationshipToPrimaryInsuredCd": "Dependent<PERSON><PERSON><PERSON>", "registryTypeId": "geroot://Customer/INDIVIDUALCUSTOMER//{{rootId_Individual}}", "_type": "CapDentalPolicyInfoInsuredDetailsEntity", "_key": {"id": "4ee624a6-2dad-3c79-a6a9-9f2d6eb93ca0", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "f19b0267-016d-34f3-a238-9e0e59e695b3"}}], "isVerified": false, "plan": "High", "planCategory": "PPO", "policyPaidToDate": "2022-10-27", "policyPaidToDateWithGracePeriod": "2022-10-27", "policyStatus": "Active", "productCd": "DNIndividual", "term": {"effectiveDate": "2020-10-27T13:44:24.154Z", "expirationDate": "2023-10-27T13:44:24.154Z", "_type": "Term", "_key": {"id": "bf7ebba1-8394-3a45-ad5f-ca2e9f2f2975", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "f19b0267-016d-34f3-a238-9e0e59e695b3"}}, "_key": {"id": "f19b0267-016d-34f3-a238-9e0e59e695b3", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b"}}, "procedureCode": "D0330", "submittedFee": {"amount": 114.17, "currency": "USD"}, "dateOfService": "2022-03-30", "toothCodes": ["6"], "predetInd": false, "ortho": {"downPayment": {"amount": 100, "currency": "USD"}, "appliancePlacedDate": "2022-02-18", "orthoFrequencyCd": "OneTime", "orthoMonthQuantity": 2, "_type": "CapDentalOrthodonticEntity", "_key": {"id": "a209ab1d-6b43-3d2a-b08c-b978361b413a", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b"}}, "diagnosisCodes": [{"code": "string1", "qualifier": "AB", "_type": "CapDentalDiagnosisCodeEntity", "_key": {"id": "bee272a8-2313-3c9f-b4dc-a7d85034d81e", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b"}}], "_type": "CapDentalProcedureEntity", "_key": {"id": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "360ac470-c2e4-3288-8549-176e74e702e0"}}], "patient": {"_type": "CapDentalPatientEntity", "_key": {"id": "fcf91373-8890-33bd-a9ea-8e5ec7f75bed", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "360ac470-c2e4-3288-8549-176e74e702e0"}}, "_type": "CapDentalClaimInfoEntity", "_key": {"id": "360ac470-c2e4-3288-8549-176e74e702e0", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5"}}, "settlementNumber": "S7972", "settlementResult": {"proposal": "PAY", "entries": [{"serviceSource": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b", "calculationResult": {"procedureType": "Preventive", "charge": {"currency": "USD", "amount": 114.17}, "coveredFee": {"currency": "USD", "amount": 114.17}, "payableDeductible": {"currency": "USD", "amount": 114.17}, "procedureID": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b", "submittedCode": "D0330", "coinsuranceAmt": {"currency": "USD", "amount": 0}, "coveredCode": "D0330", "patientResponsibility": {"currency": "USD", "amount": 114.17}, "consideredFee": {"currency": "USD", "amount": 114.17}, "netBenefitAmount": {"currency": "USD", "amount": 0}, "coinsurancePercentage": 50, "_type": "CapDentalCalculationResultEntity", "_key": {"id": "0eddf5c2-aba4-347c-b6e0-a11d2239b8c6", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "d11979eb-8833-3c87-bd01-971527df63c2"}}, "status": {"flag": "Allowed", "fee": {"currency": "USD", "amount": 114.17}, "procedureID": "49efe9d0-5fb5-39fb-9b44-35548bf2d20b", "submittedCode": "D0330", "coveredCode": "D0330", "_type": "CapDentalCalculationStatusEntity", "_key": {"id": "f8d8330d-2f93-30e0-ae82-440d8d23e3af", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "d11979eb-8833-3c87-bd01-971527df63c2"}}, "_type": "CapDentalResultEntryEntity", "_key": {"id": "d11979eb-8833-3c87-bd01-971527df63c2", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "84e720ce-b025-3772-aacb-5585b0083b6c"}}], "payeeRef": "geroot://Customer/INDIVIDUALCUSTOMER//5b907d2a-3785-38f0-bffb-88a907dbf373", "reservedAccumulators": [{"accumulatorType": "IndividualMaximum", "reservedAmount": {"currency": "USD", "amount": 0}, "renewalType": "Annual", "appliesToProcedureCategory": "Preventive", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "4a3b533f-17ae-3da1-9075-14c6fc15a3df", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "84e720ce-b025-3772-aacb-5585b0083b6c"}}, {"accumulatorType": "IndividualMaximum", "reservedAmount": {"currency": "USD", "amount": 0}, "renewalType": "Annual", "appliesToProcedureCategory": "Basic", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "f4647730-4411-355a-a7d0-d42c06245176", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "84e720ce-b025-3772-aacb-5585b0083b6c"}}, {"accumulatorType": "IndividualMaximum", "reservedAmount": {"currency": "USD", "amount": 0}, "renewalType": "Annual", "appliesToProcedureCategory": "Major", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "1870658f-700e-3748-99df-4a33156c3761", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "84e720ce-b025-3772-aacb-5585b0083b6c"}}, {"accumulatorType": "IndividualDeductible", "reservedAmount": {"currency": "USD", "amount": 114.17}, "renewalType": "Annual", "appliesToProcedureCategory": "Preventive", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "8dd4569c-6f94-3fa7-bc43-8fe4f3907a28", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "84e720ce-b025-3772-aacb-5585b0083b6c"}}, {"accumulatorType": "IndividualDeductible", "reservedAmount": {"currency": "USD", "amount": 114.17}, "renewalType": "Annual", "appliesToProcedureCategory": "Basic", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "c6a0fd7a-913a-3416-8bb4-8350090d16f3", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "84e720ce-b025-3772-aacb-5585b0083b6c"}}, {"accumulatorType": "IndividualDeductible", "reservedAmount": {"currency": "USD", "amount": 114.17}, "renewalType": "Annual", "appliesToProcedureCategory": "Major", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "bea21500-2c0c-364d-9b72-da4ebafa59f1", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "84e720ce-b025-3772-aacb-5585b0083b6c"}}, {"accumulatorType": "FamilyDeductible", "reservedAmount": {"currency": "USD", "amount": 114.17}, "renewalType": "Annual", "appliesToProcedureCategory": "Preventive", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "e1cd1a31-cf67-3cdc-82eb-a15a85d2de2d", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "84e720ce-b025-3772-aacb-5585b0083b6c"}}, {"accumulatorType": "FamilyDeductible", "reservedAmount": {"currency": "USD", "amount": 114.17}, "renewalType": "Annual", "appliesToProcedureCategory": "Basic", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "3deb0156-770d-3d69-9066-987fdf1908e9", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "84e720ce-b025-3772-aacb-5585b0083b6c"}}, {"accumulatorType": "FamilyDeductible", "reservedAmount": {"currency": "USD", "amount": 114.17}, "renewalType": "Annual", "appliesToProcedureCategory": "Major", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "8d44513f-a226-3bd9-99c9-8743f9a3d810", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "84e720ce-b025-3772-aacb-5585b0083b6c"}}], "paymentAmount": {"currency": "USD", "amount": 0}, "_type": "CapDentalDecisionResultEntity", "_key": {"id": "84e720ce-b025-3772-aacb-5585b0083b6c", "rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5"}}, "state": "Approved", "settlementDetail": {"_key": {"rootId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "revisionNo": 1, "parentId": "ecdb89c1-bc8f-43b9-b444-34e9e076c8c5", "id": "6582d60b-3e8e-30b0-aa43-f7ad5e4838b3"}, "_modelName": "CapDentalSettlement", "_modelType": "CapSettlement", "_modelVersion": "1", "_timestamp": "2022-05-25T10:25:28.045Z", "_type": "CapDentalSettlementDetailEntity"}}