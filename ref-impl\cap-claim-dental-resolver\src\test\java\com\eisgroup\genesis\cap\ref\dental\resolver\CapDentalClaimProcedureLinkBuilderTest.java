package com.eisgroup.genesis.cap.ref.dental.resolver;

import com.eisgroup.genesis.cap.ref.dental.link.CapDentalClaimProcedureLinkBuilder;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalProcedureEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.google.gson.JsonObject;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;

public class CapDentalClaimProcedureLinkBuilderTest {

    private static final String CLAIM_HISTORY_LINK_URI = "capPatient://claimHistory/CapDentalSettlement/ecdb89c1-bc8f-43b9-b444-34e9e076c8c5/49efe9d0-5fb5-39fb-9b44-35548bf2d20b";

    @InjectMocks
    private CapDentalClaimProcedureLinkBuilder capDentalClaimProcedureLinkBuilder;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void shouldCreateClaimProcedureLink() {
        // given
        JsonObject submittedProcedureJson = JsonUtils.load("json/submitted-procedure.json");
        CapDentalProcedureEntity submittedProcedureEntity = (CapDentalProcedureEntity) ModelInstanceFactory.createInstance("CapDentalSettlement", "1",submittedProcedureJson);

        // when
        EntityLink<CapDentalProcedureEntity> link = capDentalClaimProcedureLinkBuilder.createLink(submittedProcedureEntity);

        // then
        assertEquals(link.getURIString(), CLAIM_HISTORY_LINK_URI);
    }
}
