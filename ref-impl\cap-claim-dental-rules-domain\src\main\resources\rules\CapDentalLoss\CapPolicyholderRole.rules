Namespace CapDentalLoss


Rule "MandatoryPolicyholderRegistryId" On CapPolicyholderRole.registryId {
  Description "Policyholder RegistryId is Mandatory."
  Set Mandatory
  Error "MandatoryPolicyholderRegistryId": "Policyholder registryId is required."
}

Rule "MandatoryPolicyholderRoleCd" on CapPolicyholderRole.roleCd {
  Description "Policyholder roleCd is Mandatory."
  Assert Count(roleCd[this == "Member"]) > 0
  Error "MandatoryPolicyholderRoleCd": "Policyholder roleCd is required."
}