Namespace CapDentalLoss

Rule "AllowedCannotBeNegative" On CapDentalProcedureCoordinationOfBenefitsEntity.allowed {
  Description "EISDEVTS-39873"
  Assert CapDentalProcedureCoordinationOfBenefitsEntity.allowed >= 0
  Error "AllowedCannotBeNegative": "cob.allowed cannot be negative."
}

Rule "ConsideredCannotBeNegative" On CapDentalProcedureCoordinationOfBenefitsEntity.considered {
  Description "EISDEVTS-39873"
  Assert CapDentalProcedureCoordinationOfBenefitsEntity.considered >= 0
  Error "ConsideredCannotBeNegative": "cob.considered cannot be negative."
}

Rule "PaidCannotBeNegative" On CapDentalProcedureCoordinationOfBenefitsEntity.paid {
  Description "EISDEVTS-39873"
  Assert CapDentalProcedureCoordinationOfBenefitsEntity.paid >= 0
  Error "PaidCannotBeNegative": "cob.paid cannot be negative."
}