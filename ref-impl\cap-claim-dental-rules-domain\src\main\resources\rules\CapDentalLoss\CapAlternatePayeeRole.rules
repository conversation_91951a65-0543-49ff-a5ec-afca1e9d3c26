Namespace CapDentalLoss


Rule "MandatoryAlternatePayeeRegistryId" On CapAlternatePayeeRole.registryId {
  Description "AlternatePayee RegistryId is Mandatory."
  When CapDentalClaimDataEntity.payeeType = "AlternatePayee"
  Set Mandatory
  Error "MandatoryAlternatePayeeRegistryId": "AlternatePayee registryId is required."
}

Rule "MandatoryAlternatePayeeRoleCd" on CapAlternatePayeeRole.roleCd {
  Description "AlternatePayee roleCd is Mandatory."
  When CapDentalClaimDataEntity.payeeType = "AlternatePayee"
  Assert Count(roleCd[this == "AlternatePayee"]) > 0
  Error "MandatoryAlternatePayeeRoleCd": "AlternatePayee roleCd is required."
}