Namespace CapDentalLoss

EntryPoint "ClaimReopenAction" {
  "ResetClaimReopenReason"
}

EntryPoint "ClaimSubmitAction" {
  "MandatoryClaimPatient",
  "DefaultClaimPayeeType",
  "MandatoryClaimPayeeType",
  "MandatoryClaimPolicyHolder",
  "MandatoryClaimProvider",
  "MandatoryClaimReceivedDate",
  "ReceivedDateCannotBeInFuture",
  "DefaultReceivedDate",
  "MandatoryClaimSource",
  "DefaultSource",
  "MandatoryClaimTransactionType",
  "DefaultTransactionType",
  "MandatoryOneOrthoServiceForClaim",
  "MandatoryActualOrPredeterminationActualServicesForClaim",
  "MandatoryClaimPolicyId",
  "DownPaymentCannotBeNegative",
  "MandatoryOrthodonticFrequency",
  "MandatoryProcedureDateOfService",
  "DateOfServiceCannotBeInFuture",
  "DateOfServiceCannotBeAfterReceivedDateWhenCleanClaimDateIsBlank",
  "MandatoryProcedureCode",
  "ProcedureQuantityMin",
  "MandatoryProcedureQuantity",
  "ProcedureQuantityMax",
  "SubmittedFeeCannotBeNegative",
  "MandatorySubmittedFee",
  "HideAutoAccidentState",
  "HideDateOfAccident",
  "HideDateApplicancePlaced",
  "HideDownPaymentAmount",
  "HidePriorProsthesisPlacement",
  "MandatoryClaimPartyRoleCd",
  "MandatoryPatientRegistryId",
  "MandatoryPatientRoleCd",
  "MandatoryPolicyholderRegistryId",
  "MandatoryPolicyholderRoleCd",
  "MandatoryProviderRegistryId",
  "MandatoryProviderlink",
  "MandatoryProviderRoleCd",
  "MandatoryAlternatePayeeRegistryId",
  "MandatoryAlternatePayeeRoleCd"
}

EntryPoint "ClaimSuspendAction" {
  "SuspendLossOnlyAvailableForOrtho"
}

EntryPoint "ClaimUpdateValidation" {
  "MandatoryClaimPatient",
  "DefaultClaimPayeeType",
  "MandatoryClaimPayeeType",
  "MandatoryClaimPolicyHolder",
  "MandatoryClaimProvider",
  "MandatoryClaimReceivedDate",
  "ReceivedDateCannotBeInFuture",
  "DefaultReceivedDate",
  "MandatoryClaimSource",
  "DefaultSource",
  "MandatoryClaimTransactionType",
  "DefaultTransactionType",
  "MandatoryOneOrthoServiceForClaim",
  "MandatoryActualOrPredeterminationActualServicesForClaim",
  "MandatoryClaimPolicyId",
  "DownPaymentCannotBeNegative",
  "MandatoryOrthodonticFrequency",
  "MandatoryProcedureDateOfService",
  "DateOfServiceCannotBeInFuture",
  "DateOfServiceCannotBeAfterReceivedDateWhenCleanClaimDateIsBlank",
  "MandatoryProcedureCode",
  "ProcedureQuantityMin",
  "MandatoryProcedureQuantity",
  "ProcedureQuantityMax",
  "SubmittedFeeCannotBeNegative",
  "MandatorySubmittedFee",
  "HideAutoAccidentState",
  "HideDateOfAccident",
  "HideDateApplicancePlaced",
  "HideDownPaymentAmount",
  "HidePriorProsthesisPlacement",
  "MandatoryClaimPartyRoleCd",
  "MandatoryPatientRegistryId",
  "MandatoryPatientRoleCd",
  "MandatoryPolicyholderRegistryId",
  "MandatoryPolicyholderRoleCd",
  "MandatoryProviderRegistryId",
  "MandatoryProviderlink",
  "MandatoryProviderRoleCd",
  "MandatoryAlternatePayeeRegistryId",
  "MandatoryAlternatePayeeRoleCd"
}

EntryPoint "UIPolicyAndPatientValidation" {
  "MandatoryPatientRegistryId",
  "MandatoryClaimPolicyId"
}

EntryPoint "UIClaimInfoValidation" {
  "DefaultClaimPayeeType",
  "MandatoryClaimPayeeType",
  "MandatoryClaimPolicyHolder",
  "MandatoryProviderlink",
  "MandatoryClaimReceivedDate",
  "ReceivedDateCannotBeInFuture",
  "DefaultReceivedDate",
  "MandatoryClaimSource",
  "DefaultSource",
  "MandatoryClaimTransactionType",
  "DefaultTransactionType",
  "MandatoryOneOrthoServiceForClaim",
  "MandatoryActualOrPredeterminationActualServicesForClaim",
  "DownPaymentCannotBeNegative",
  "MandatoryProcedureDateOfService",
  "DateOfServiceCannotBeInFuture",
  "DateOfServiceCannotBeAfterReceivedDateWhenCleanClaimDateIsBlank",
  "MandatoryProcedureCode",
  "ProcedureQuantityMin",
  "MandatoryProcedureQuantity",
  "ProcedureQuantityMax",
  "SubmittedFeeCannotBeNegative",
  "MandatorySubmittedFee",
  "HideAutoAccidentState",
  "HideDateOfAccident"
}
