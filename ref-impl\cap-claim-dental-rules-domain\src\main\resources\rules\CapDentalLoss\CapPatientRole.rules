Namespace CapDentalLoss


Rule "MandatoryClaimPartyRoleCd" On ClaimPartyRole.roleCd {
  Assert Count(roleCd) > 0
  Error "MandatoryClaimPartyRoleCd": "Roles is required."
}

Rule "MandatoryPatientRegistryId" On CapPatientRole.registryId {
  Description "Patient registryId is Mandatory."
  Set Mandatory
  Error "MandatoryPatientRegistryId": "Patient is required."
}

Rule "MandatoryPatientRoleCd" on CapPatientRole.roleCd {
  Description "Patient roleCd is Mandatory."
  Assert Count(roleCd[this == "SubjectOfClaims"]) > 0
  Error "MandatoryPatientRoleCd": "Patient roleCd is required."
}


Rule "MandatoryProviderRegistryId" On CapProviderRole.registryId {
  Description "Provider RegistryId is Mandatory."
  When CapDentalClaimDataEntity.isUnknownOrIntProvider != true
  Set Mandatory
  Error "MandatoryProviderRegistryId": "Provider registryId is required."
}

Rule "MandatoryProviderlink" On CapProviderRole.providerLink {
  Description "Provider link is Mandatory."
  When CapDentalClaimDataEntity.isUnknownOrIntProvider != true
  Set Mandatory
  Error "MandatoryProviderRegistryId": "Provider link is required."
}

Rule "MandatoryProviderRoleCd" on CapProviderRole.roleCd {
  Description "Provider roleCd is Mandatory."
  When CapDentalClaimDataEntity.isUnknownOrIntProvider != true
  Assert Count(roleCd[this == "IndividualProvider" || this == "OrganizationProvider"]) > 0
  Error "MandatoryProviderRoleCd": "Provider roleCd is required."
}